import { FilterFilled } from '@ant-design/icons';
import { Button, DatePicker, Input, InputNumber, Select, Space } from 'antd';
import dayjs from 'dayjs';
import type { Key } from 'react';
import React, { useState } from 'react';
import { Tag } from '../types/cashflow';
import { FilterDropdownProps } from '../types/table';
import { parseCurrency } from './helpers';

const { Option } = Select;

interface TextSearchFilterProps extends FilterDropdownProps {
  dataIndex: string;
  title?: string;
}

// Text Search Filter Component
const TextSearchFilter: React.FC<TextSearchFilterProps> = ({
  setSelectedKeys,
  selectedKeys,
  confirm,
  clearFilters,
  dataIndex,
  title,
}) => {
  return (
    <div style={{ padding: 8 }}>
      <Input
        placeholder={`Search ${title || dataIndex}`}
        value={selectedKeys[0]}
        onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
        style={{ marginBottom: 8, display: 'block' }}
      />
      <Space>
        <Button type='primary' onClick={() => confirm()} size='small' style={{ width: 90 }}>
          Filter
        </Button>
        <Button onClick={() => clearFilters?.()} size='small' style={{ width: 90 }}>
          Reset
        </Button>
      </Space>
    </div>
  );
};

interface NumericFilterProps extends FilterDropdownProps {
  isCurrency?: boolean;
}

// Numeric Filter Component
const NumericFilter: React.FC<NumericFilterProps> = ({
  setSelectedKeys,
  confirm,
  clearFilters,
  isCurrency = false,
}) => {
  const [mode, setMode] = useState<'exact' | 'range'>('exact');
  const [min, setMin] = useState<string>('');
  const [max, setMax] = useState<string>('');
  const [exact, setExact] = useState<string>('');

  const handleSearch = () => {
    if (mode === 'exact') {
      const value = exact ? (isCurrency ? parseCurrency(exact) : Number(exact)) : null;
      setSelectedKeys(value ? [value.toString()] : []);
    } else {
      if (!min && !max) {
        setSelectedKeys([]);
      } else {
        const minValue = min ? (isCurrency ? parseCurrency(min) : Number(min)) : undefined;
        const maxValue = max ? (isCurrency ? parseCurrency(max) : Number(max)) : undefined;
        setSelectedKeys([`${minValue || ''}-${maxValue || ''}`]);
      }
    }
    confirm();
  };

  const handleReset = () => {
    setMin('');
    setMax('');
    setExact('');
    clearFilters?.();
  };

  return (
    <div style={{ padding: 8, minWidth: 210 }}>
      <Space style={{ marginBottom: 8 }} direction='vertical'>
        <Select value={mode} onChange={setMode} style={{ width: '100%' }}>
          <Option value='exact'>Exact Value</Option>
          <Option value='range'>Range</Option>
        </Select>

        {mode === 'exact' ? (
          <InputNumber
            value={exact}
            onChange={val => setExact(val?.toString() || '')}
            placeholder='Enter exact value'
            style={{ width: '100%' }}
            formatter={(value: string | number | undefined) =>
              value
                ? isCurrency
                  ? `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                  : value.toString()
                : ''
            }
            parser={(value: string | undefined) => (value ? value.replace(/\$\s?|(,*)/g, '') : '')}
          />
        ) : (
          <>
            <InputNumber
              value={min}
              onChange={val => setMin(val?.toString() || '')}
              placeholder='No Min'
              style={{ width: '100%' }}
              formatter={(value: string | number | undefined) =>
                value
                  ? isCurrency
                    ? `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                    : value.toString()
                  : ''
              }
              parser={(value: string | undefined) =>
                value ? value.replace(/\$\s?|(,*)/g, '') : ''
              }
            />
            <InputNumber
              value={max}
              onChange={val => setMax(val?.toString() || '')}
              placeholder='No Max'
              style={{ width: '100%' }}
              formatter={(value: string | number | undefined) =>
                value
                  ? isCurrency
                    ? `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                    : value.toString()
                  : ''
              }
              parser={(value: string | undefined) =>
                value ? value.replace(/\$\s?|(,*)/g, '') : ''
              }
            />
          </>
        )}
      </Space>
      <Space style={{ display: 'flex', justifyContent: 'space-between', marginTop: 8 }}>
        <Button type='primary' onClick={handleSearch} size='small'>
          Filter
        </Button>
        <Button onClick={handleReset} size='small'>
          Reset
        </Button>
      </Space>
    </div>
  );
};

// Date Range Filter Component
const DateRangeFilter: React.FC<FilterDropdownProps> = ({
  setSelectedKeys,
  confirm,
  clearFilters,
}) => {
  const [startDate, setStartDate] = useState<dayjs.Dayjs | null>(null);
  const [endDate, setEndDate] = useState<dayjs.Dayjs | null>(null);

  const handleSearch = () => {
    if (!startDate && !endDate) {
      setSelectedKeys([]);
    } else {
      const start = startDate?.format('YYYY-MM-DD');
      const end = endDate?.format('YYYY-MM-DD');
      setSelectedKeys([`${start || ''}-${end || ''}`]);
    }
    confirm();
  };

  const handleReset = () => {
    setStartDate(null);
    setEndDate(null);
    clearFilters?.();
  };

  return (
    <div style={{ padding: 8, minWidth: 210 }}>
      <Space direction='vertical' style={{ width: '100%' }}>
        <DatePicker
          format='MMM D, YYYY'
          value={startDate}
          onChange={setStartDate}
          placeholder='No Start Date'
          style={{ width: '100%' }}
        />
        <DatePicker
          format='MMM D, YYYY'
          value={endDate}
          onChange={setEndDate}
          placeholder='No End Date'
          style={{ width: '100%' }}
        />
        <Space style={{ display: 'flex', justifyContent: 'space-between' }}>
          <Button type='primary' onClick={handleSearch} size='small'>
            Filter
          </Button>
          <Button onClick={handleReset} size='small'>
            Reset
          </Button>
        </Space>
      </Space>
    </div>
  );
};

export const getColumnSearchProps = (dataIndex: string, title?: string) => ({
  filterDropdown: (props: FilterDropdownProps) => (
    <TextSearchFilter {...props} dataIndex={dataIndex} title={title} />
  ),
  filterIcon: (filtered: boolean) => (
    <FilterFilled style={{ color: filtered ? '#1890ff' : undefined }} />
  ),
  onFilter: (value: any, record: any) => {
    debugger;
    const result = record[dataIndex]
      ? record[dataIndex].toString().toLowerCase().includes(value.toLowerCase())
      : false;
    console.log('result', result);
    return result;
  },
});

export const getParentChildSearchProps = (dataIndex: string, title?: string) => ({
  filterDropdown: (props: FilterDropdownProps) => (
    <TextSearchFilter {...props} dataIndex={dataIndex} title={title} />
  ),
  filterIcon: (filtered: boolean) => (
    <FilterFilled style={{ color: filtered ? '#1890ff' : undefined }} />
  ),
  onFilter: (value: boolean | Key, record: any): boolean => {
    if (typeof value === 'boolean') return false;

    // For parent rows, check the project name
    if (!record.isChild) {
      return record.project.name.toLowerCase().includes(String(value).toLowerCase());
    }

    // For child rows, check the parent project name
    return record.project.name.toLowerCase().includes(String(value).toLowerCase());
  },
});

export const getNumericColumnSearchProps = (dataIndex: string, isCurrency: boolean = false) => ({
  filterDropdown: (props: FilterDropdownProps) => (
    <NumericFilter {...props} isCurrency={isCurrency} />
  ),
  filterIcon: (filtered: boolean) => (
    <FilterFilled style={{ color: filtered ? '#1890ff' : undefined }} />
  ),
  onFilter: (value: any, record: any) => {
    const recordValue = isCurrency ? parseCurrency(record[dataIndex]) : Number(record[dataIndex]);

    if (value.includes('-')) {
      const [minStr, maxStr] = value.split('-');
      const min = minStr ? Number(minStr) : undefined;
      const max = maxStr ? Number(maxStr) : undefined;

      if (min !== undefined && max !== undefined) {
        return recordValue >= min && recordValue <= max;
      } else if (min !== undefined) {
        return recordValue >= min;
      } else if (max !== undefined) {
        return recordValue <= max;
      }
      return true;
    }
    return recordValue === Number(value);
  },
});

export const getTagColumnSearchProps = (allTags: Tag[]) => ({
  filterDropdown: ({
    setSelectedKeys,
    selectedKeys,
    confirm,
    clearFilters,
  }: FilterDropdownProps) => (
    <div style={{ padding: 8 }}>
      <Select
        mode='multiple'
        allowClear
        style={{ width: 188, marginBottom: 8, display: 'block' }}
        placeholder='Search tags'
        value={selectedKeys as Key[]}
        onChange={setSelectedKeys}
        options={allTags
          .slice()
          .sort((a, b) => a.name.localeCompare(b.name))
          .map(tag => ({
            label: tag.name,
            value: tag.name,
          }))}
        filterOption={(inputValue, option) =>
          option ? option.label.toString().toLowerCase().includes(inputValue.toLowerCase()) : false
        }
      />
      <Space>
        <Button type='primary' onClick={() => confirm()} size='small' style={{ width: 90 }}>
          Filter
        </Button>
        <Button
          onClick={() => {
            if (clearFilters) {
              clearFilters();
            }
            confirm();
          }}
          size='small'
          style={{ width: 90 }}
        >
          Reset
        </Button>
      </Space>
    </div>
  ),
  filterIcon: (filtered: boolean) => (
    <FilterFilled style={{ color: filtered ? '#1890ff' : undefined }} />
  ),
  onFilter: (value: Key | boolean, record: { tags?: { name: string }[] }) => {
    if (typeof value === 'boolean') {
      return false;
    }
    if (typeof value !== 'string') {
      return false;
    }
    return record.tags ? record.tags.some(tag => tag.name === value) : false;
  },
});

export const getDateRangeSearchProps = (dataIndex: string) => ({
  filterDropdown: (props: FilterDropdownProps) => <DateRangeFilter {...props} />,
  filterIcon: (filtered: boolean) => (
    <FilterFilled style={{ color: filtered ? '#1890ff' : undefined }} />
  ),
  onFilter: (value: any, record: any) => {
    if (!value.includes('-')) return true;
    const [startStr, endStr] = value.split('-');
    const recordDate = dayjs(record[dataIndex]);

    if (startStr && endStr) {
      return (
        recordDate.unix() >= dayjs(startStr).unix() && recordDate.unix() <= dayjs(endStr).unix()
      );
    } else if (startStr) {
      return recordDate.unix() >= dayjs(startStr).unix();
    } else if (endStr) {
      return recordDate.unix() <= dayjs(endStr).unix();
    }
    return true;
  },
});
