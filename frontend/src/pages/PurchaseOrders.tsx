import { CloseOutlined, DeleteOutlined, EditOutlined, SaveOutlined } from '@ant-design/icons';
import {
  Tag as AntdTag,
  <PERSON>ton,
  Card,
  DatePicker,
  Form,
  Input,
  InputNumber,
  Popconfirm,
  Select,
  Space,
  Spin,
  Table,
  Typography,
  message,
} from 'antd';
import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';
import ClearFiltersButton from '../components/common/clearFiltersButton';
import PurchaseOrderFormFields from '../components/forms/PurchaseOrderFormFields';
import { useOrganization } from '../contexts/OrganizationContext';
import { useTableControls } from '../hooks/useTableControls';
import {
  addPurchaseOrder,
  createTag,
  deletePurchaseOrder,
  getProjectCategoryTags,
  getProjectExpenses,
  getPurchaseOrders,
  getTags,
  updatePurchaseOrder,
} from '../services/api';
import { Project, ProjectExpense, PurchaseOrder, Tag, UpsertPurchaseOrder } from '../types';
import { calculateDueDate, formatCurrency, parseCurrency } from '../utils/helpers';
import {
  getColumnSearchProps,
  getDateRangeSearchProps,
  getNumericColumnSearchProps,
  getTagColumnSearchProps,
} from '../utils/tableFilters';

const { Title } = Typography;
const { Option } = Select;

const getApiErrorMessage = (error: any, defaultMessage: string): string => {
  if (error.response?.data?.detail) {
    const detail = error.response.data.detail;
    if (Array.isArray(detail)) {
      return detail.map((err: any) => err.msg || JSON.stringify(err)).join(', ');
    }
    return detail;
  }
  return defaultMessage;
};

const PurchaseOrders: React.FC = () => {
  const [form] = Form.useForm<UpsertPurchaseOrder>();
  const [purchaseOrders, setPurchaseOrders] = useState<PurchaseOrder[]>([]);
  const [projects, setProjects] = useState<Project[]>([]);
  const [projectExpenses, setProjectExpenses] = useState<ProjectExpense[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [editingKey, setEditingKey] = useState<string | number>('');
  const [editForm] = Form.useForm<UpsertPurchaseOrder>();
  const [selectedProject, setSelectedProject] = useState<number | null>(null);
  const [allTags, setAllTags] = useState<Tag[]>([]);
  const [tagOptions, setTagOptions] = useState<{ label: string; value: string }[]>([]);
  const [categoryTagOptions, setCategoryTagOptions] = useState<{ label: string; value: string }[]>(
    []
  );
  const { filteredInfo, sortedInfo, handleTableChange, clearFiltersAndSorting } = useTableControls({
    initialSort: { columnKey: 'issue_date', order: 'descend' },
  });
  const { organization } = useOrganization();

  useEffect(() => {
    fetchPurchaseOrders();
    fetchProjects();
    fetchAllTags();
  }, []);

  useEffect(() => {
    if (selectedProject) {
      fetchProjectExpenses(selectedProject);
      fetchProjectCategoryTags(selectedProject);
    }
  }, [selectedProject]);

  // Add CSS for table cell alignment
  useEffect(() => {
    const style = document.createElement('style');
    style.textContent = `
      .purchase-orders-table .ant-table-tbody > tr > td {
        vertical-align: top !important;
        padding-top: 8px !important;
      }
      .purchase-orders-table .ant-form-item {
        margin-bottom: 0 !important;
      }
    `;
    document.head.appendChild(style);
    return () => {
      document.head.removeChild(style);
    };
  }, []);

  const fetchPurchaseOrders = async (): Promise<void> => {
    try {
      setLoading(true);
      const response = await getPurchaseOrders();
      const purchaseOrders = response.data;
      setPurchaseOrders(
        Array.isArray(purchaseOrders)
          ? purchaseOrders.map((po: any) => ({
              ...po,
              key: po.po_number,
            }))
          : []
      );
    } catch (error: any) {
      console.error('Error fetching purchase orders:', error);
      const errorMessage = getApiErrorMessage(error, 'Failed to load purchase orders');
      message.error(errorMessage);
      setPurchaseOrders([]);
    } finally {
      setLoading(false);
    }
  };

  const fetchProjects = async (): Promise<void> => {
    try {
      const response = await fetch('/api/purchase-order-projects');
      if (!response.ok) throw new Error('Failed to fetch projects');
      const projectsData = await response.json();
      setProjects(projectsData);
    } catch (error) {
      console.error('Error fetching projects:', error);
      const errorMessage = getApiErrorMessage(error, 'Failed to load projects');
      message.error(errorMessage);
      setProjects([]);
    }
  };

  const fetchProjectExpenses = async (projectId: number): Promise<void> => {
    try {
      const res = await getProjectExpenses(projectId);
      setProjectExpenses(res.data);
    } catch (error) {
      console.error('Error fetching expenses:', error);
      const errorMessage = getApiErrorMessage(error, 'Failed to load expenses');
      message.error(errorMessage);
      setProjectExpenses([]);
    }
  };

  const fetchAllTags = async () => {
    try {
      const response = await getTags();
      const sortedTags = response.data.sort((a: Tag, b: Tag) => a.name.localeCompare(b.name));
      setAllTags(sortedTags);
      setTagOptions(
        sortedTags.map(tag => ({
          label: tag.name,
          value: tag.name,
        }))
      );
    } catch (error: any) {
      console.error('Error fetching tags:', error);
      const errorMessage = getApiErrorMessage(error, 'Failed to load tags');
      message.error(errorMessage);
    }
  };

  const fetchProjectCategoryTags = async (projectId: number) => {
    try {
      console.log('Fetching category tags for project:', projectId);
      const response = await getProjectCategoryTags(projectId);
      const categoryTags = response.data;
      console.log('Category tags received:', categoryTags);
      setCategoryTagOptions(
        categoryTags.map(tag => ({
          label: tag.name,
          value: tag.name,
        }))
      );
      console.log(
        'Category tag options set:',
        categoryTags.map(tag => ({ label: tag.name, value: tag.name }))
      );
    } catch (error) {
      console.error('Error fetching project category tags:', error);
      setCategoryTagOptions([]);
    }
  };

  const processTags = async (tags: string[] | undefined): Promise<number[]> => {
    if (!organization) {
      message.error('Organization not found');
      return [];
    }
    try {
      if (!tags) {
        return [];
      }
      const tagIds: number[] = [];
      const newTags: Tag[] = [];
      for (const tagName of tags) {
        const existingTag = allTags.find(tag => tag.name === tagName);
        if (existingTag) {
          tagIds.push(existingTag.id);
        } else {
          const trimmedTagName = tagName.trim();
          const response = await createTag({
            id: 0,
            name: trimmedTagName,
            organization_id: organization.id,
          });
          const tagObject = response.data;
          if (tagObject) {
            tagIds.push(tagObject.id);
            newTags.push(tagObject);
          }
        }
      }

      const updatedTags = [...allTags, ...newTags].sort((a, b) => a.name.localeCompare(b.name));
      setAllTags(updatedTags);
      setTagOptions(
        updatedTags.map(tag => ({
          label: tag.name,
          value: tag.name,
        }))
      );

      return tagIds;
    } catch (error) {
      console.error('Error processing tags:', error);
      message.error('Failed to process tags');
      return [];
    }
  };

  const processCategoryTag = async (tagName: string): Promise<number> => {
    if (!organization) {
      message.error('Organization not found');
      return 0;
    }
    const trimmedTagName = tagName?.trim();
    if (!trimmedTagName) {
      return 0;
    }
    try {
      const existingTag = allTags.find(tag => tag.name === trimmedTagName);
      if (existingTag) {
        return existingTag.id;
      } else {
        const response = await createTag({
          id: 0,
          name: trimmedTagName,
          organization_id: organization.id,
        });
        const tagObject = response.data;
        if (tagObject) {
          return tagObject.id;
        }
      }
      return 0;
    } catch (error) {
      console.error('Error processing expense category tag:', error);
      message.error('Failed to process expense category tag');
      return 0;
    }
  };

  const handleSubmit = async (values: UpsertPurchaseOrder): Promise<void> => {
    try {
      setSubmitting(true);
      const tagIds = await processTags(values.tag_names);
      const categoryTagId = await processCategoryTag(values.category_tag_name?.[0] || '');
      const { tags, tag_names, category_tag_name, ...apiValues } = values;

      const formattedValues: UpsertPurchaseOrder = {
        ...apiValues,
        amount: parseCurrency(values.amount),
        tag_ids: tagIds,
        category_tag_id: categoryTagId,
      };

      await addPurchaseOrder(formattedValues);
      message.success('Purchase order added successfully');
      form.resetFields();
      fetchPurchaseOrders();
    } catch (error: any) {
      console.error('Error adding purchase order:', error);
      const errorMessage = getApiErrorMessage(error, 'Failed to add purchase order');
      message.error(errorMessage);
    } finally {
      setSubmitting(false);
    }
  };

  const handleDelete = async (id: string | number): Promise<void> => {
    try {
      await deletePurchaseOrder(id);
      message.success('Purchase order deleted successfully');
      fetchPurchaseOrders();
    } catch (error: any) {
      console.error('Error deleting purchase order:', error);
      const errorMessage = getApiErrorMessage(error, 'Failed to delete purchase order');
      message.error(errorMessage);
    }
  };

  const isEditing = (record: PurchaseOrder): boolean => record.id === editingKey;

  const edit = (record: PurchaseOrder): void => {
    if (!record.id) return;
    const project = projects.find(p => p.name === record.project_name);
    if (project) {
      fetchProjectExpenses(project.id);
      fetchProjectCategoryTags(project.id);
    } else {
      setProjectExpenses([]);
      setCategoryTagOptions([]);
    }
    editForm.setFieldsValue({
      ...record,
      issue_date: dayjs(record.issue_date),
      amount: parseCurrency(record.amount),
      description: record.description,
      tag_names: record.tags ? record.tags.map(tag => tag.name) : [],
      category_tag_name: record.category_tag ? record.category_tag.name : undefined,
      due_date: record.due_date ? dayjs(record.due_date) : undefined,
    });
    setEditingKey(record.id);
  };

  const cancel = (): void => {
    setEditingKey('');
  };

  const save = async (id: string | number): Promise<void> => {
    try {
      const row = (await editForm.validateFields()) as UpsertPurchaseOrder;
      const tagIds = await processTags(row.tag_names);
      const categoryTagId = await processCategoryTag(row.category_tag_name?.[0] || '');
      const { tags, tag_names, category_tag_name, ...rest } = row;

      const updatedPO: UpsertPurchaseOrder = {
        ...rest,
        id: id as number,
        tag_ids: tagIds,
        category_tag_id: categoryTagId,
      };

      await updatePurchaseOrder(updatedPO);
      message.success('PO updated successfully');
      setEditingKey('');
      fetchPurchaseOrders();
    } catch (error) {
      console.error('Error updating PO:', error);
      message.error('Failed to update PO');
    }
  };

  const handleProjectChange = (value: number): void => {
    const project = projects.find(p => p.id === value);
    if (project) {
      setSelectedProject(value);
      form.setFieldsValue({
        project_id: value,
        tag_names: [],
        category_tag_name: undefined,
      });
      fetchProjectExpenses(value);
      fetchProjectCategoryTags(value);
    }
  };

  const columns = [
    {
      title: 'Project',
      dataIndex: 'project_name',
      key: 'project_name',
      width: '12%',
      sorter: (a: PurchaseOrder, b: PurchaseOrder) => {
        const aName = a.project_name || '';
        const bName = b.project_name || '';
        return aName.localeCompare(bName);
      },
      sortOrder: sortedInfo.columnKey === 'project_name' ? sortedInfo.order : undefined,
      filteredValue: filteredInfo.project_name || null,
      ...getColumnSearchProps('project_name', 'Project Name'),
    },
    {
      title: 'PO Number',
      dataIndex: 'po_number',
      key: 'po_number',
      width: '10%',
      sorter: (a: PurchaseOrder, b: PurchaseOrder) => a.po_number.localeCompare(b.po_number),
      sortOrder: sortedInfo.columnKey === 'po_number' ? sortedInfo.order : undefined,
      filteredValue: filteredInfo.po_number || null,
      ...getColumnSearchProps('po_number', 'PO Number'),
      render: (text: string, record: PurchaseOrder) => {
        const editable = isEditing(record);
        return editable ? (
          <Form.Item
            name='po_number'
            rules={[{ required: true, message: 'Please enter a PO number' }]}
          >
            <Input />
          </Form.Item>
        ) : (
          text
        );
      },
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      width: '18%',
      ...getColumnSearchProps('description', 'Description'),
      filteredValue: filteredInfo.description || null,
      render: (text: string, record: PurchaseOrder) => {
        const editable = isEditing(record);
        return editable ? (
          <Form.Item name='description'>
            <Input.TextArea rows={1} placeholder='Enter description' />
          </Form.Item>
        ) : (
          <div style={{ whiteSpace: 'pre-wrap', maxWidth: '200px' }}>
            {text || <span style={{ color: '#999' }}>No description</span>}
          </div>
        );
      },
    },
    {
      title: 'Tags',
      dataIndex: 'tags',
      key: 'tags',
      width: '12%',
      ...getTagColumnSearchProps(allTags),
      filteredValue: filteredInfo.tags || null,
      render: (tags: Tag[] | undefined, record: PurchaseOrder) => {
        const editable = isEditing(record);
        return editable ? (
          <Form.Item name='tag_names'>
            <Select
              mode='tags'
              style={{ width: '100%' }}
              placeholder='Select or create tags'
              tokenSeparators={[',']}
              options={tagOptions}
            />
          </Form.Item>
        ) : (
          <>
            {(tags || []).map(tag => (
              <AntdTag key={tag.id}>{tag.name}</AntdTag>
            ))}
          </>
        );
      },
    },
    {
      title: 'Expense Category',
      dataIndex: 'category_tag',
      key: 'category_tag',
      width: '10%',
      render: (category_tag: Tag | undefined, record: PurchaseOrder) => {
        const editable = isEditing(record);
        return editable ? (
          <Form.Item
            name='category_tag_name'
            rules={[{ required: true, message: 'Please select an expense category' }]}
          >
            <Select
              showSearch
              allowClear
              placeholder={
                categoryTagOptions.length === 0
                  ? 'No expense categories available'
                  : 'Select category'
              }
              options={categoryTagOptions}
              filterOption={(inputValue, option) =>
                option?.label.toUpperCase().indexOf(inputValue.toUpperCase()) !== -1
              }
            />
          </Form.Item>
        ) : category_tag ? (
          <AntdTag color='blue'>{category_tag.name}</AntdTag>
        ) : (
          <span style={{ color: '#999' }}>No category</span>
        );
      },
    },
    {
      title: 'Issue Date',
      dataIndex: 'issue_date',
      key: 'issue_date',
      width: '10%',
      sorter: (a: PurchaseOrder, b: PurchaseOrder) =>
        dayjs(a.issue_date).unix() - dayjs(b.issue_date).unix(),
      sortOrder: sortedInfo.columnKey === 'issue_date' ? sortedInfo.order : undefined,
      filteredValue: filteredInfo.issue_date || null,
      ...getDateRangeSearchProps('issue_date'),
      defaultSortOrder: 'descend' as const,
      render: (date: dayjs.Dayjs, record: PurchaseOrder) => {
        const editable = isEditing(record);
        return editable ? (
          <Form.Item
            name='issue_date'
            rules={[{ required: true, message: 'Please select an issue date' }]}
          >
            <DatePicker format='MMM D, YYYY' style={{ width: '100%' }} />
          </Form.Item>
        ) : (
          date.format('YYYY-MM-DD')
        );
      },
    },
    {
      title: 'Lead Time (weeks)',
      dataIndex: 'lead_time',
      key: 'lead_time',
      width: '8%',
      sorter: (a: PurchaseOrder, b: PurchaseOrder) => a.lead_time - b.lead_time,
      sortOrder: sortedInfo.columnKey === 'lead_time' ? sortedInfo.order : undefined,
      filteredValue: filteredInfo.lead_time || null,
      ...getNumericColumnSearchProps('lead_time', false),
      render: (text: number, record: PurchaseOrder) => {
        const editable = isEditing(record);
        return editable ? (
          <Form.Item
            name='lead_time'
            rules={[{ required: true, message: 'Please enter lead time' }]}
          >
            <InputNumber min={1} style={{ width: '100%' }} />
          </Form.Item>
        ) : (
          text
        );
      },
    },
    {
      title: 'Due Date',
      dataIndex: 'due_date',
      key: 'due_date',
      width: '10%',
      sorter: (a: PurchaseOrder, b: PurchaseOrder) =>
        dayjs(a.due_date).unix() - dayjs(b.due_date).unix(),
      sortOrder: sortedInfo.columnKey === 'due_date' ? sortedInfo.order : undefined,
      filteredValue: filteredInfo.due_date || null,
      ...getDateRangeSearchProps('due_date'),
      render: (date: dayjs.Dayjs, record: PurchaseOrder) => {
        const editable = isEditing(record);
        if (!editable) {
          return date ? date.format('YYYY-MM-DD') : 'Not calculated';
        }
        // Use Form.Item with shouldUpdate to watch dependencies
        return (
          <Form.Item
            shouldUpdate={(prev, curr) =>
              prev.issue_date !== curr.issue_date ||
              prev.lead_time !== curr.lead_time ||
              prev.terms !== curr.terms
            }
            noStyle
          >
            {() => {
              const issueDate = editForm.getFieldValue('issue_date');
              const leadTime = editForm.getFieldValue('lead_time');
              const terms = editForm.getFieldValue('terms');
              let calculatedDate = null;
              if (issueDate && leadTime && terms) {
                calculatedDate = calculateDueDate(issueDate, leadTime, terms);
                // Set the calculated due date as a form field value
                editForm.setFieldsValue({ due_date: calculatedDate });
              } else {
                // Clear the due date field if not all required fields are filled
                editForm.setFieldsValue({ due_date: undefined });
              }
              return (
                <div
                  style={{
                    padding: '4px 11px',
                    border: '1px solid #d9d9d9',
                    borderRadius: '6px',
                    backgroundColor: '#fafafa',
                    minHeight: '32px',
                    display: 'flex',
                    alignItems: 'center',
                  }}
                >
                  {calculatedDate
                    ? calculatedDate.format('YYYY-MM-DD')
                    : 'Calculated automatically'}
                </div>
              );
            }}
          </Form.Item>
        );
      },
    },
    {
      title: 'Amount',
      dataIndex: 'amount',
      key: 'amount',
      width: '10%',
      sorter: (a: PurchaseOrder, b: PurchaseOrder) => a.amount - b.amount,
      sortOrder: sortedInfo.columnKey === 'amount' ? sortedInfo.order : undefined,
      filteredValue: filteredInfo.amount || null,
      ...getNumericColumnSearchProps('amount', true),
      render: (amount: number, record: PurchaseOrder) => {
        const editable = isEditing(record);
        return editable ? (
          <Form.Item name='amount' rules={[{ required: true, message: 'Please enter an amount' }]}>
            <InputNumber
              formatter={value => `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
              parser={value => value!.replace(/\$\s?|(,*)/g, '')}
              style={{ width: '100%' }}
            />
          </Form.Item>
        ) : (
          formatCurrency(amount)
        );
      },
    },
    {
      title: 'Terms',
      dataIndex: 'terms',
      key: 'terms',
      width: '10%',
      sorter: (a: PurchaseOrder, b: PurchaseOrder) => a.terms.localeCompare(b.terms),
      sortOrder: sortedInfo.columnKey === 'terms' ? sortedInfo.order : undefined,
      filteredValue: filteredInfo.terms || null,
      render: (text: string, record: PurchaseOrder) => {
        const editable = isEditing(record);
        return editable ? (
          <Form.Item name='terms' rules={[{ required: true, message: 'Please select terms' }]}>
            <Select>
              <Option value='100% due upon receipt'>100% due upon receipt</Option>
              <Option value='Net 30'>Net 30</Option>
              <Option value='Net 60'>Net 60</Option>
              <Option value='Net 90'>Net 90</Option>
            </Select>
          </Form.Item>
        ) : (
          text
        );
      },
    },
    {
      title: 'Actions',
      key: 'actions',
      width: '10%',
      render: (_: any, record: PurchaseOrder) => {
        const editable = isEditing(record);
        return editable ? (
          <Space>
            <Button
              icon={<SaveOutlined />}
              type='primary'
              onClick={() => record.id && save(record.id)}
              size='small'
            >
              Save
            </Button>
            <Button icon={<CloseOutlined />} onClick={cancel} size='small'>
              Cancel
            </Button>
          </Space>
        ) : (
          <Space>
            <Button
              icon={<EditOutlined />}
              type='primary'
              disabled={editingKey !== ''}
              onClick={() => edit(record)}
              size='small'
            >
              Edit
            </Button>
            <Popconfirm
              title='Are you sure you want to delete this PO?'
              onConfirm={() => record.id && handleDelete(record.id)}
              okText='Yes'
              cancelText='No'
            >
              <Button danger icon={<DeleteOutlined />} size='small' disabled={editingKey !== ''}>
                Delete
              </Button>
            </Popconfirm>
          </Space>
        );
      },
    },
  ];

  return (
    <div>
      <Title level={2}>Purchase Orders</Title>

      <Card title='Add Purchase Order' style={{ marginBottom: 16 }}>
        <Form form={form} layout='vertical' onFinish={handleSubmit}>
          <PurchaseOrderFormFields
            projects={projects}
            projectExpenses={projectExpenses}
            tagOptions={tagOptions}
            handleProjectChange={handleProjectChange}
            selectedProject={selectedProject}
            isEditing={false}
            categoryTagOptions={categoryTagOptions}
            form={form}
          />
          <Form.Item
            style={{
              alignSelf: 'flex-end',
              marginBottom: '0',
              flex: 1,
              minWidth: '150px',
            }}
          >
            <Button
              type='primary'
              htmlType='submit'
              loading={submitting}
              style={{ width: '100%', marginTop: '24px' }}
            >
              Add Purchase Order
            </Button>
          </Form.Item>
        </Form>
      </Card>

      <Card title='Purchase Orders'>
        <Form form={editForm} component={false}>
          {/* Hidden due_date field to store the calculated value */}
          <Form.Item name='due_date' hidden>
            <Input />
          </Form.Item>
          <ClearFiltersButton onClear={clearFiltersAndSorting} />
          {loading ? (
            <div style={{ textAlign: 'center', padding: '20px' }}>
              <Spin size='large' />
            </div>
          ) : (
            <Table
              columns={columns}
              dataSource={purchaseOrders}
              rowKey='id'
              pagination={{ defaultPageSize: 10, showSizeChanger: true }}
              scroll={{ x: 'max-content' }}
              onChange={handleTableChange}
              className='purchase-orders-table'
            />
          )}
        </Form>
      </Card>
    </div>
  );
};

export default PurchaseOrders;
